# 快速上手指南

## 🚀 5分钟快速体验

### 步骤1：环境准备
```bash
# 确保已安装Java 11+和Maven 3.6+
java -version
mvn -version
```

### 步骤2：一键启动
```bash
# Windows环境
start-encrypted-app.bat

# Linux/Mac环境
./start-encrypted-app.sh
```

**启动脚本功能**：
1. 🧹 清理旧文件
2. 📦 Maven构建项目（自动加密敏感类）
3. 🚀 启动Spring Boot应用

### 步骤3：选择启动模式
启动时选择运行模式：
- **[1] 生产模式**: 使用默认密钥
- **[2] 调试模式**: 详细日志输出
- **[3] 自定义密钥**: 输入自定义解密密钥

### 步骤4：测试功能
```bash
# 服务状态检查
curl http://localhost:8080/service-status

# 加密类调用测试
curl http://localhost:8080/run-secure-direct

# 性能统计查看
curl http://localhost:8080/performance-stats

# 完整API测试（可选）
./test-controller-apis.sh
```

## 📋 项目结构

```
encrypted-guard-demo/
├── src/main/java/com/example/
│   ├── EncryptedDemoApplication.java    # Spring Boot启动类
│   ├── controller/TestController.java   # REST API控制器
│   ├── security/                        # 安全组件
│   │   ├── EncryptedClassLoader.java    # 自定义类加载器
│   │   ├── EncryptedService.java        # 加密服务
│   │   └── KeyProvider.java             # 密钥管理
│   ├── secure/                          # 敏感业务逻辑（会被加密）
│   │   ├── service/                     # 敏感服务类
│   │   ├── dao/                         # 敏感数据访问类
│   │   └── entity/                      # 敏感实体类
│   ├── build/EncryptTool.java           # 构建期加密工具
│   ├── monitoring/PerformanceMonitor.java # 性能监控
│   └── config/                          # 配置组件
├── encrypted/                           # 运行时加密文件目录
├── target/springboot-encrypted-demo.jar # 主JAR包
├── start-encrypted-app.sh               # Linux/Mac启动脚本
├── start-encrypted-app.bat              # Windows启动脚本
├── test-controller-apis.sh              # API测试脚本
└── pom.xml                              # Maven配置
```

## 🔍 核心原理

### 1. 构建期加密
Maven构建时自动加密secure包下的敏感类文件，生成.class加密文件到encrypted目录。

### 2. 运行期解密
Spring Boot启动时使用自定义ClassLoader透明解密并加载加密类，支持依赖注入和AOP。

## 🛠️ 主要API接口

### 基础功能
- `GET /service-status` - 查看服务状态
- `GET /run-secure-direct` - 调用加密服务
- `GET /run-advanced` - 高级加密服务

### 事务功能
- `GET /test-transaction` - 事务测试
- `GET /test-transaction-rollback` - 事务回滚测试
- `GET /test-database-insert` - 数据库操作测试

### 监控功能
- `GET /performance-stats` - 性能统计
- `GET /reset-performance-stats` - 重置统计

## 🔧 常见问题

### 启动失败
- 检查Java版本（需要11+）
- 确保Maven构建成功
- 查看encrypted目录是否存在加密文件

### 密钥错误
- 确保加密和解密使用相同密钥
- 检查系统属性decrypt.key设置

### 类加载失败
- 确认secure包下的类已被正确加密
- 检查EncryptedClassLoader配置

## 🎯 扩展实验

### 1. 修改敏感逻辑
编辑`src/main/java/com/example/secure/service/`下的服务类，重新构建测试。

### 2. 自定义密钥
使用启动脚本的自定义密钥模式，测试不同密钥的效果。

### 3. 性能测试
使用`test-controller-apis.sh`脚本进行批量API测试，观察性能统计。

## 📚 相关文档

- `docs/技术知识点详解.md` - 深入技术原理
- `docs/代码加密混淆迁移指南.md` - 技术迁移指南
- `specs/Spring集成加密类加载/` - 完整技术方案

## ⚡ 快速开始

1. 运行启动脚本体验基本功能
2. 使用API测试脚本验证所有接口
3. 查看性能统计了解运行状态
4. 根据需要修改敏感类进行实验
