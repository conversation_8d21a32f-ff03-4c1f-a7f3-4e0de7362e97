### 手动验证
```bash
# 1. 检查主JAR包
ls -la target/springboot-encrypted-demo.jar

# 2. 检查加密文件
ls -la encrypted/com/example/secure/
file encrypted/com/example/secure/*.class

# 3. 检查JAR包内容
jar -tf target/springboot-encrypted-demo.jar | grep "com/example/secure"
# 应该没有输出

# 4. 验证应用功能
curl http://localhost:8080/service-status
curl http://localhost:8080/run-secure-direct
```

## 🌐 功能验证接口

### 基础功能测试
```bash
# 服务状态检查 - 验证所有加密服务是否正常注入
curl http://localhost:8080/service-status

# 直接调用加密服务 - 测试SecretLogic核心功能
curl http://localhost:8080/run-secure-direct

# 高级加密服务 - 测试AdvancedSecretService
curl "http://localhost:8080/run-advanced?input=test-data"

# 兼容性接口 - 旧版本手动加载方式
curl http://localhost:8080/run-secure
```

### Spring特性测试
```bash
# 权限验证 - 测试加密类中的权限逻辑
curl "http://localhost:8080/validate-permission?userId=admin&operation=READ_DATA"

# 业务规则验证 - 测试复杂业务逻辑
curl "http://localhost:8080/validate-business-rule?userId=admin&data=test&operation=READ"

# 敏感数据处理 - 测试数据处理功能
curl "http://localhost:8080/process-sensitive-data?data=confidential-info"
```

### 事务功能测试
```bash
# 基础事务测试 - 验证@Transactional注解
curl http://localhost:8080/test-transaction

# 事务回滚测试 - 验证异常回滚机制
curl "http://localhost:8080/test-transaction-rollback?shouldFail=true"

# 嵌套事务测试 - 验证事务传播行为
curl http://localhost:8080/test-nested-transaction

# 只读事务测试 - 验证只读事务功能
curl "http://localhost:8080/test-readonly-transaction?query=test-query"
```

### 性能监控与统计
```bash
# 性能统计查看 - 获取详细的性能报告
curl http://localhost:8080/performance-stats

# 重置性能统计 - 清空统计数据
curl http://localhost:8080/reset-performance-stats

# 缓存统计 - 查看类加载缓存情况
curl http://localhost:8080/cache-stats
```
